import React from "react";
import {
    Text,
    TouchableOpacity,
    View,
    ActivityIndicator,
    FlatList,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { navigationService } from "../src/services/navigationService";

// Custom hooks
import { useChatManagement } from "../src/hooks/useChatManagement";
import { useChatFiltering } from "../src/hooks/useChatFiltering";

// Components
import { ChatItem } from "../src/components/chatManagement/ChatItem";
import { BulkActions } from "../src/components/chatManagement/BulkActions";
import { SearchBar } from "../src/components/chatManagement/SearchBar";
import { FilterModal } from "../src/components/chatManagement/FilterModal";
import { EmptyState } from "../src/components/chatManagement/EmptyState";

export default function ChatManagement() {
  // Use custom hooks for state management
  const {
    chats,
    loading,
    selectedChats,
    searchQuery,
    filterType,
    sortBy,
    showBulkActions,
    showFilterModal,
    refreshing,
    loadChats,
    handleRefresh,
    toggleChatSelection,
    selectAllChats,
    clearSelection,
    handleBulkArchive,
    handleBulkDelete,
    handleBulkMute,
    setSearchQuery,
    setFilterType,
    setSortBy,
    setShowFilterModal,
  } = useChatManagement();

  // Use filtering hook
  const { filteredChats } = useChatFiltering({
    chats,
    searchQuery,
    filterType,
    sortBy,
  });

  // Render individual chat item
  const renderChatItem = ({ item }: { item: any }) => (
    <ChatItem
      item={item}
      isSelected={selectedChats.has(item.id)}
      onPress={toggleChatSelection}
    />
  );

  // Loading state
  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor: '#F8F9FA' }}>
        <ActivityIndicator size="large" color="#87CEEB" />
        <Text style={{ marginTop: 16, fontSize: 16, color: '#666' }}>Loading chats...</Text>
      </View>
    );
  }

  return (
    <View style={{ flex: 1, backgroundColor: '#F8F9FA' }}>
      <StatusBar style="light" />
      
      {/* Header */}
      <LinearGradient
        colors={['#87CEEB', '#4682B4']}
        style={{
          paddingTop: 50,
          paddingBottom: 20,
          paddingHorizontal: 20,
        }}
      >
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 16 }}>
          <TouchableOpacity
            onPress={() => navigationService.goBack()}
            style={{ marginRight: 16 }}
          >
            <Ionicons name="arrow-back" size={24} color="#FFFFFF" />
          </TouchableOpacity>
          <Text style={{ fontSize: 20, fontWeight: 'bold', color: '#FFFFFF', flex: 1 }}>
            Chat Management
          </Text>
          <TouchableOpacity
            onPress={() => setShowFilterModal(true)}
            style={{ marginLeft: 16 }}
          >
            <Ionicons name="filter" size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <SearchBar
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          onClearSearch={() => setSearchQuery('')}
        />
      </LinearGradient>

      {/* Bulk Actions Bar */}
      {showBulkActions && (
        <BulkActions
          selectedCount={selectedChats.size}
          onSelectAll={selectAllChats}
          onClearSelection={clearSelection}
          onArchive={handleBulkArchive}
          onMute={handleBulkMute}
          onDelete={handleBulkDelete}
        />
      )}

      {/* Chat List */}
      <View style={{ flex: 1, paddingHorizontal: 20, paddingTop: 20 }}>
        {filteredChats.length === 0 ? (
          <EmptyState />
        ) : (
          <FlatList
            data={filteredChats}
            renderItem={renderChatItem}
            keyExtractor={(item) => item.id}
            refreshing={refreshing}
            onRefresh={handleRefresh}
            showsVerticalScrollIndicator={false}
            contentContainerStyle={{ paddingBottom: 20 }}
          />
        )}
      </View>

      {/* Filter Modal */}
      <FilterModal
        visible={showFilterModal}
        filterType={filterType}
        sortBy={sortBy}
        onClose={() => setShowFilterModal(false)}
        onFilterChange={setFilterType}
        onSortChange={setSortBy}
      />
    </View>
  );
}
